<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Card Viewer (Vue)</title>
    <style>
        :root {
            --color-border-primary: #e0e0e0;
            --color-text-muted: #9e9e9e;
            --color-card-bg: #ffffff;
            --color-card-border: #eeeeee;
            --color-bg-secondary: #f5f5f5; /* Used for placeholders and default cover bg */
            --color-bg-tertiary: #f7f7f7; /* Used for note container and some placeholder text bg - updated for contrast */
            --color-text-primary: #212121;
            --color-text-secondary: #757575;
            --color-wave-tertiary: rgba(0, 0, 0, 0.03); 
            --color-wave-secondary: rgba(0, 0, 0, 0.06); 
            --font-family-main: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            --color-button-bg: #007bff;
            --color-button-text: #ffffff;
            --color-app-bg: #e9edf0; /* Updated for contrast */
        }

        body {
            font-family: var(--font-family-main);
            margin: 0;
            padding: 20px;
            background-color: var(--color-app-bg);
            color: var(--color-text-primary);
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            box-sizing: border-box;
        }

        #root {
            width: 100%;
            max-width: 380px; 
        }

        .app-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px; 
            padding-top: 20px; 
        }

        .toggle-button {
            padding: 10px 20px;
            font-size: 15px;
            font-family: var(--font-family-main);
            color: var(--color-button-text);
            background-color: var(--color-button-bg);
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.1s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-weight: 500;
        }

        .toggle-button:hover {
            background-color: #0056b3;
        }
        .toggle-button:active {
            transform: scale(0.98);
        }
        
        .toggle-button:focus-visible {
            outline: 3px solid #0056b3;
            outline-offset: 2px;
        }

        .card-wrapper {
             width: 100%;
             max-width: 350px; 
        }

        /* Styles for BookCard */
        .book {
            width: 100%;
            font-family: var(--font-family-main);
            font-size: 12px;
            color: var(--color-text-secondary);
            font-weight: 700;
            cursor: pointer;
            transition: transform 0.2s ease;
            box-sizing: border-box;
        }

        .book:hover:not(.loading-wave) {
            transform: translateY(-2px);
        }
        
        .book:focus-visible:not(.loading-wave) {
          outline: 2px solid var(--color-button-bg);
          outline-offset: 3px;
          border-radius: 10px; 
          transform: translateY(-2px); 
        }

        .book.loading-wave {
            cursor: not-allowed;
            position: relative;
            overflow: hidden;
            border-radius: 10px;
        }

        .book-container {
            border-radius: 10px;
            background-color: var(--color-card-bg);
            border: 1px solid var(--color-card-border);
            display: flex;
            width: 100%;
            padding: 12px;
            flex-direction: column;
            align-items: start;
            box-sizing: border-box;
            overflow: hidden; 
        }

        /* Actual Book Cover Styles (when not loading) */
        .book-cover {
            border-radius: 5px;
            background-color: var(--color-card-bg); 
            border: 1px solid var(--color-card-border);
            width: 100%;
            height: 400px;
            max-width: 100%;
            position: relative;
            overflow: hidden;
            flex-shrink: 0;
            margin-bottom: 12px;
        }

        .blurred-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            filter: blur(10px) brightness(0.7);
            transform: scale(1.1);
            z-index: 0;
            opacity: 0.8;
        }

        .main-cover {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            mask: radial-gradient(ellipse 85% 90% at center, black 100%, transparent 100%);
            -webkit-mask: radial-gradient(ellipse 85% 90% at center, black 100%, transparent 100%);
        }

        .default-cover {
            background-color: var(--color-bg-secondary);
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1; 
            border-radius: 5px;
        }
        
        .default-cover svg {
            width: 32px; 
            height: 40px;
        }
        .default-cover rect { fill: var(--color-border-primary); }
        .default-cover path { fill: var(--color-text-muted); }

        /* Placeholder Styles (during loading) */
        .book-cover-placeholder {
            width: 100%;
            height: 400px;
            background-color: var(--color-bg-secondary);
            border: 1px solid var(--color-card-border);
            border-radius: 5px;
            margin-bottom: 12px;
            flex-shrink: 0;
        }

        .placeholder-block {
            background-color: var(--color-bg-tertiary);
            border-radius: 4px;
            min-height: 1em; 
        }

        .placeholder-text-lg { height: 20px; width: 75%; margin-top: 0; margin-bottom: 8px; } /* Removed composes */
        .placeholder-text-md { height: 16px; width: 60%; margin-top: 3px; margin-bottom: 8px; } /* Removed composes */
        .placeholder-text-sm { height: 12px; width: 45%; margin-top: 3px; margin-bottom: 3px; } /* Removed composes */
        
        .placeholder-divider-line { height: 1px; width: 100%; margin: 10px 0; background-color: var(--color-border-primary); }
        
        .placeholder-note-area { 
            height: 38px; 
            width: 100%; 
            margin-bottom: 15px; 
            border-radius: 5px; 
            background-color: var(--color-bg-tertiary); 
            border: 1px solid var(--color-border-primary);
            box-sizing: border-box;
        }
        
        .placeholder-rating-section {
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: space-between;
            padding-top: 3px;
        }
        .placeholder-rating-section .placeholder-text-sm:first-child { width: 40%; margin:0; }
        .placeholder-rating-section .placeholder-text-sm:last-child { width: 20%; margin:0; }


        /* Actual Text Content Styles */
        .book-title {
            color: var(--color-text-primary);
            font-size: 16px;
            margin-top: 0; 
            margin-bottom: 0;
            font-weight: 600;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .author {
            font-weight: 400;
            margin-top: 3px;
            margin-bottom: 0;
            color: var(--color-text-secondary);
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 12px;
        }

        .divider {
            background-color: var(--color-border-primary);
            width: 100%;
            height: 1px;
            border: none;
            margin: 10px 0;
            flex-shrink: 0;
        }

        .recent-note { /* Heading "Recent Note" */
            font-weight: 600;
            color: var(--color-text-secondary);
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 0 0 8px 0;
            font-size: 12px;
        }

        .note-container {
            border-radius: 5px;
            background-color: var(--color-bg-tertiary);
            border: 1px solid var(--color-border-primary); /* Added for contrast */
            width: 100%;
            padding: 6px 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            flex-shrink: 0;
            margin-bottom: 15px;
        }

        .note-container .note-title {
            color: var(--color-text-primary) !important;
            font-weight: 400;
            width: calc(100% - 20px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 0;
            font-size: 12px;
        }
        
        .recent-note-icon {
            width: 14px;
            height: 14px;
            object-fit: contain;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s ease;
            flex-shrink: 0;
            color: var(--color-text-secondary);
        }
        .recent-note-icon:hover, .recent-note-icon:focus {
            opacity: 1;
            color: var(--color-text-primary);
        }
         .recent-note-icon:focus-visible {
          outline: 2px solid var(--color-button-bg);
          outline-offset: 1px;
          border-radius: 3px;
        }

        .rating-section {
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: space-between;
            font-weight: 400;
            flex-shrink: 0;
            padding-top: 3px;
            font-size: 11px;
        }

        .rating-container, .interaction-container {
            display: flex;
            align-items: center;
            gap: 4px;
            color: var(--color-text-secondary); 
        }
        
        .rating-text, .interaction-count { 
            color: var(--color-text-secondary);
            margin: 0; 
        }

        .icon {
            width: 14px;
            height: 14px;
            object-fit: contain;
            object-position: center;
        }
        .rating-container .icon { color: #f5c518; }
        .interaction-container .icon { color: var(--color-text-muted); }


        .book.loading-wave::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%; 
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent 0%,
                var(--color-wave-tertiary) 20%,
                var(--color-wave-secondary) 50%,
                var(--color-wave-tertiary) 80%,
                transparent 100%
            );
            animation: wave 2.0s ease-in-out infinite; 
            z-index: 10; 
            border-radius: 10px; 
        }

        @keyframes wave {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Content Reveal Animation */
        @keyframes contentReveal {
            0% {
                opacity: 0;
                filter: blur(5px);
                transform: translateY(10px);
            }
            100% {
                opacity: 1;
                filter: blur(0px);
                transform: translateY(0px);
            }
        }

        .animate-reveal {
            opacity: 0; 
            animation-name: contentReveal;
            animation-duration: 0.5s;
            animation-timing-function: ease-out;
            animation-fill-mode: forwards;
        }
        
        /* Apply placeholder-block styles directly to placeholder elements */
        .placeholder-text-lg, .placeholder-text-md, .placeholder-text-sm {
            background-color: var(--color-bg-tertiary);
            border-radius: 4px;
        }

        .book-title-author-block.animate-reveal { margin-top: 0; } 
        .divider-block.animate-reveal { margin-top: 0; }
        .recent-note-heading-block.animate-reveal { margin-top: 0; }
        .note-container-block.animate-reveal { margin-top: 0; }
        .rating-section-block.animate-reveal { margin-top: 0; }

    </style>
</head>
<body>
    <div id="root"></div>
    <script type="module">
        const { createApp, ref, computed } = Vue;

        const BookCard = {
            props: {
                book: {
                    type: Object,
                    required: true
                }
            },
            setup(props) {
                const hasValidCover = computed(() => !!(props.book.cover_media_url || props.book.cover_url));
                const coverImageUrl = computed(() => props.book.cover_media_url || props.book.cover_url);

                const coverImageStyle = computed(() => coverImageUrl.value ? {
                    backgroundImage: `url("${coverImageUrl.value}")`,
                    backgroundSize: 'auto 100%',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                } : {});

                const blurredBackgroundStyle = computed(() => hasValidCover.value && coverImageUrl.value ? {
                    backgroundImage: `url("${coverImageUrl.value}")`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                } : {});

                const handleClick = () => {
                    if (props.book.isLoading || (props.book.id && props.book.id > 1000000000000)) {
                        console.log('BookCard: Click ignored (loading or temporary ID). Title:', props.book.title);
                        return;
                    }
                    console.log('BookCard: Clicked. Title:', props.book.title);
                };

                const openRecentNote = (event) => {
                    if (!props.book.recentNote) return;
                    event.stopPropagation();
                    console.log('BookCard: Open recent note. Title:', props.book.recentNote.title);
                };

                const handleKeyDown = (event) => {
                    if (event.key === 'Enter' || event.key === ' ') {
                        event.preventDefault();
                        if (!props.book.isLoading) handleClick();
                    }
                };
                
                const handleIconKeyDown = (event, action) => {
                    if (event.key === 'Enter' || event.key === ' ') {
                        event.preventDefault();
                        action(event);
                    }
                };

                return {
                    hasValidCover,
                    coverImageUrl,
                    coverImageStyle,
                    blurredBackgroundStyle,
                    handleClick,
                    openRecentNote,
                    handleKeyDown,
                    handleIconKeyDown,
                };
            },
            template: \`
                <article 
                  :class="['book', { 'loading-wave': book.isLoading }]" 
                  @click="book.isLoading ? null : handleClick()"
                  @keydown="handleKeyDown"
                  role="button" 
                  :tabindex="book.isLoading ? -1 : 0"
                  :aria-label="'View details for ' + book.title"
                  :aria-busy="book.isLoading"
                >
                    <div class="book-container">
                        <template v-if="book.isLoading">
                            <div class="book-cover-placeholder"></div>
                            <div class="placeholder-text-lg placeholder-block"></div>
                            <div class="placeholder-text-md placeholder-block"></div>
                            <div class="placeholder-divider-line placeholder-block"></div>
                            <div class="placeholder-text-md placeholder-block" style="width: 40%; margin-bottom: 8px;"></div>
                            <div class="placeholder-note-area placeholder-block"></div>
                            <div class="placeholder-rating-section">
                                <div class="placeholder-text-sm placeholder-block"></div>
                                <div class="placeholder-text-sm placeholder-block"></div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="book-cover animate-reveal" :style="{ animationDelay: '0s' }" aria-hidden="true">
                                <template v-if="hasValidCover && coverImageUrl">
                                    <div class="blurred-background" :style="blurredBackgroundStyle"></div>
                                    <div class="main-cover" :style="coverImageStyle"></div>
                                </template>
                                <template v-if="!hasValidCover">
                                    <div class="default-cover">
                                        <svg width="32" height="40" viewBox="0 0 32 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width="32" height="40" rx="2" />
                                            <path d="M8 12h16v2H8v-2zm0 4h16v2H8v-2zm0 4h12v2H8v-2z" />
                                        </svg>
                                    </div>
                                </template>
                            </div>

                            <div class="book-title-author-block animate-reveal" :style="{ animationDelay: '0.1s' }">
                                <h2 class="book-title" :title="book.title">{{ book.title }}</h2>
                                <p class="author" :title="book.author || 'Unknown Author'">{{ book.author || 'Unknown Author' }}</p>
                            </div>

                            <div class="divider-block animate-reveal" :style="{ animationDelay: '0.15s' }">
                                <hr class="divider" />
                            </div>
                            
                            <div class="recent-note-heading-block animate-reveal" :style="{ animationDelay: '0.2s' }">
                                <h3 class="recent-note">Recent Note</h3>
                            </div>

                            <div class="note-container-block animate-reveal" :style="{ animationDelay: '0.25s' }">
                                <div class="note-container">
                                    <template v-if="book.recentNote">
                                        <p class="note-title" :title="book.recentNote.title">{{ book.recentNote.title }}</p>
                                        <img
                                            src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgZmlsbD0iY3VycmVudENvbG9yIiB2aWV3Qm94PSIwIDAgMTYgMTYiPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTYgMmMwIC41NS40NSAxIDEgMWgzLjU5bC05LjMgOS4yOWEuNzUuNzUgMCAxIDAgMS4wNiAxLjA2TDExLjQ0IDUuNDFWM2MwIC41NS40NSAxIDEgMXMuNDUtMSAxLTFWMkg2eiIvPjwvc3ZnPg=="
                                            alt="Open recent note"
                                            class="recent-note-icon"
                                            @click="openRecentNote"
                                            @keydown="event => handleIconKeyDown(event, openRecentNote)"
                                            title="Open this note"
                                            role="button"
                                            tabindex="0"
                                        />
                                    </template>
                                    <template v-else>
                                        <p class="note-title">No notes yet</p>
                                    </template>
                                </div>
                            </div>

                            <div class="rating-section-block animate-reveal" :style="{ animationDelay: '0.3s' }">
                                <div class="rating-section">
                                    <div class="rating-container">
                                        <p class="rating-text">Your rating: {{ book.rating || 0 }}/5</p>
                                        <img
                                            src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgZmlsbD0iY3VycmVudENvbG9yIiB2aWV3Qm94PSIwIDAgMTYgMTYiPjxwYXRoIGQ9Ik0zLjYxMiAxNS40YS43NS43NSAwIDAgMS0xLjA4LTEuMDJsMS44NTQtMy44MjQtMi44Ny0yLjk5NGEuNzUuNzUgMCAwIDEgLjQyLTEuMzA1bDMuOTk0LS41ODFMOC44OC43N2EuNzUuNzUgMCAwIDEgMS4zNSAwbDEuNzggMy44ODcgMy45OTQuNTgzYS43NS43NSAwIDAgMSAuNDIgMS4zMDVsLTIuODcgMi45OTQgMS44NTQgMy44MjRhLjc1LjUuNzUgMCAwIDEtMS4wOCAxLjAyTDIgNy4yNXoiLz48L3N2Zz4="
                                            alt="" 
                                            aria-hidden="true"
                                            class="icon"
                                        />
                                    </div>
                                    <div class="interaction-container">
                                        <img
                                            src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgZmlsbD0iY3VycmVudENvbG9yIiB2aWV3Qm94PSIwIDAgMTYgMTYiPjxwYXRoIGQ9Ik0xIDNoMnYxMEgxcjBNNCA1aDJ2NUg0ek03IDJoMnYxMkc3WiIvPjxwYXRoIGQ9Ik0xMCAzaDJ2N2gtMiBNMTMgMGgydjEzaC0yeiIvPjwvc3ZnPg=="
                                            alt="" 
                                            aria-hidden="true"
                                            class="icon"
                                        />
                                        <span class="interaction-count" :title="'Number of notes: ' + (book.notesCount || 0)">{{ book.notesCount || 0 }}</span>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </article>
            \`
        };

        const App = {
            components: {
                BookCard
            },
            setup() {
                const isLoading = ref(true);
                const bookData = ref({
                    id: 1,
                    title: "Harry Potter: A Pop-Up Guide to Hogwarts",
                    author: "Kevin Wilson, Matthew Reinhart",
                    cover_url: "https://images.unsplash.com/photo-1589998059171-988d887df646?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTJ8fGJvb2t8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=400&q=60",
                    recentNote: { title: "Just started Chapter 3, the detail is amazing!" },
                    rating: 4,
                    notesCount: 12,
                });

                const toggleLoading = () => {
                    isLoading.value = !isLoading.value;
                };

                const currentBook = computed(() => ({
                    ...bookData.value,
                    isLoading: isLoading.value,
                }));

                return {
                    isLoading,
                    toggleLoading,
                    currentBook
                };
            },
            template: \`
                <div class="app-container">
                    <button 
                        @click="toggleLoading" 
                        class="toggle-button" 
                        :aria-pressed="isLoading"
                        aria-controls="book-card-1"
                    >
                        Toggle Loading State ({{ isLoading ? "Currently Loading" : "Currently Loaded" }})
                    </button>
                    <div class="card-wrapper" id="book-card-1">
                        <BookCard :book="currentBook" />
                    </div>
                </div>
            \`
        };
        
        // Load Vue from CDN
        const vueScript = document.createElement('script');
        vueScript.src = 'https://unpkg.com/vue@3.4.31/dist/vue.global.js'; // Using Vue 3 global build
        vueScript.onload = () => {
            const { createApp, ref, computed } = Vue; // Vue is now global

            // Re-assign components with Vue methods after Vue is loaded
            // This is a bit of a workaround because components are defined before Vue is global
            // A better way in a real app would be to ensure Vue loads first, then define components.
            // For simplicity here, we re-initialise or ensure Vue is accessible.
            // The initial definitions of BookCard and App can't use Vue.ref, Vue.computed directly
            // So, they are defined again here, or this script block is deferred.

            // Let's redefine components here for clarity after Vue has loaded
             const FinalBookCard = {
                props: { book: { type: Object, required: true } },
                setup(props) {
                    const { computed } = Vue;
                    const hasValidCover = computed(() => !!(props.book.cover_media_url || props.book.cover_url));
                    const coverImageUrl = computed(() => props.book.cover_media_url || props.book.cover_url);
                    const coverImageStyle = computed(() => coverImageUrl.value ? { backgroundImage: \`url("\${coverImageUrl.value}")\`, backgroundSize: 'auto 100%', backgroundPosition: 'center', backgroundRepeat: 'no-repeat'} : {});
                    const blurredBackgroundStyle = computed(() => hasValidCover.value && coverImageUrl.value ? { backgroundImage: \`url("\${coverImageUrl.value}")\`, backgroundSize: 'cover', backgroundPosition: 'center', backgroundRepeat: 'no-repeat'} : {});
                    const handleClick = () => { if (props.book.isLoading || (props.book.id && props.book.id > 1000000000000)) { console.log('BookCard: Click ignored', props.book.title); return; } console.log('BookCard: Clicked', props.book.title); };
                    const openRecentNote = (event) => { if (!props.book.recentNote) return; event.stopPropagation(); console.log('BookCard: Open recent note', props.book.recentNote.title); };
                    const handleKeyDown = (event) => { if (event.key === 'Enter' || event.key === ' ') { event.preventDefault(); if (!props.book.isLoading) handleClick(); }};
                    const handleIconKeyDown = (event, action) => { if (event.key === 'Enter' || event.key === ' ') { event.preventDefault(); action(event); }};
                    return { hasValidCover, coverImageUrl, coverImageStyle, blurredBackgroundStyle, handleClick, openRecentNote, handleKeyDown, handleIconKeyDown };
                },
                template: BookCard.template // Reuse template from above
            };

            const FinalApp = {
                components: { BookCard: FinalBookCard },
                setup() {
                    const { ref, computed } = Vue;
                    const isLoading = ref(true);
                    const bookData = ref({ id: 1, title: "Harry Potter: A Pop-Up Guide to Hogwarts", author: "Kevin Wilson, Matthew Reinhart", cover_url: "https://images.unsplash.com/photo-1589998059171-988d887df646?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTJ8fGJvb2t8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=400&q=60", recentNote: { title: "Just started Chapter 3, the detail is amazing!" }, rating: 4, notesCount: 12 });
                    const toggleLoading = () => { isLoading.value = !isLoading.value; };
                    const currentBook = computed(() => ({ ...bookData.value, isLoading: isLoading.value }));
                    return { isLoading, toggleLoading, currentBook };
                },
                template: App.template // Reuse template from above
            };
            
            createApp(FinalApp).mount('#root');
        };
        document.head.appendChild(vueScript);

    </script>
</body>
</html>


import { motion, Transition } from 'framer-motion';
import { useEffect, useRef, useState, useMemo } from 'react';

type BlurTextProps = {
  text?: string;
  delay?: number;
  className?: string;
  animateBy?: 'words' | 'letters';
  direction?: 'top' | 'bottom';
  threshold?: number;
  rootMargin?: string;
  animationFrom?: Record<string, string | number>;
  animationTo?: Array<Record<string, string | number>>;
  easing?: (t: number) => number;
  onAnimationComplete?: () => void;
  stepDuration?: number;
};

const buildKeyframes = (
  from: Record<string, string | number>,
  steps: Array<Record<string, string | number>>
): Record<string, Array<string | number>> => {
  const keys = new Set<string>([
    ...Object.keys(from),
    ...steps.flatMap((s) => Object.keys(s)),
  ]);

  const keyframes: Record<string, Array<string | number>> = {};
  keys.forEach((k) => {
    keyframes[k] = [from[k], ...steps.map((s) => s[k])];
  });
  return keyframes;
};

const BlurText: React.FC<BlurTextProps> = ({
  text = '',
  delay = 200,
  className = '',
  animateBy = 'words',
  direction = 'top',
  threshold = 0.1,
  rootMargin = '0px',
  animationFrom,
  animationTo,
  easing = (t) => t,
  onAnimationComplete,
  stepDuration = 0.35,
}) => {
  const elements = animateBy === 'words' ? text.split(' ') : text.split('');
  const [inView, setInView] = useState(false);
  const ref = useRef<HTMLParagraphElement>(null);

  useEffect(() => {
    if (!ref.current) return;
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
          observer.unobserve(ref.current as Element);
        }
      },
      { threshold, rootMargin }
    );
    observer.observe(ref.current);
    return () => observer.disconnect();
  }, [threshold, rootMargin]);

  const defaultFrom = useMemo(
    () =>
      direction === 'top'
        ? { filter: 'blur(10px)', opacity: 0, y: -50 }
        : { filter: 'blur(10px)', opacity: 0, y: 50 },
    [direction]
  );

  const defaultTo = useMemo(
    () => [
      {
        filter: 'blur(5px)',
        opacity: 0.5,
        y: direction === 'top' ? 5 : -5,
      },
      { filter: 'blur(0px)', opacity: 1, y: 0 },
    ],
    [direction]
  );

  const fromSnapshot = animationFrom ?? defaultFrom;
  const toSnapshots = animationTo ?? defaultTo;

  const stepCount = toSnapshots.length + 1;
  const totalDuration = stepDuration * (stepCount - 1);
  const times = Array.from({ length: stepCount }, (_, i) =>
    stepCount === 1 ? 0 : i / (stepCount - 1)
  );

  return (
    <p
      ref={ref}
      className={className}
      style={{ display: 'flex', flexWrap: 'wrap' }}
    >
      {elements.map((segment, index) => {
        const animateKeyframes = buildKeyframes(fromSnapshot, toSnapshots);

        const spanTransition: Transition = {
          duration: totalDuration,
          times,
          delay: (index * delay) / 1000,
        };
        (spanTransition as any).ease = easing;

        return (
          <motion.span
            key={index}
            initial={fromSnapshot}
            animate={inView ? animateKeyframes : fromSnapshot}
            transition={spanTransition}
            onAnimationComplete={
              index === elements.length - 1 ? onAnimationComplete : undefined
            }
            style={{
              display: 'inline-block',
              willChange: 'transform, filter, opacity',
            }}
          >
            {segment === ' ' ? '\u00A0' : segment}
            {animateBy === 'words' && index < elements.length - 1 && '\u00A0'}
          </motion.span>
        );
      })}
    </p>
  );
};

export default BlurText;



<template>
  <div
    ref="container"
    :class="props.class"
  >
    <Motion
      v-for="(child, index) in children"
      :key="index"
      ref="childElements"
      as="div"
      :initial="getInitial()"
      :while-in-view="getAnimate()"
      :transition="{
        duration: props.duration,
        easing: 'easeInOut',
        delay: props.delay * index,
      }"
    >
      <component :is="child" />
    </Motion>
  </div>
</template>

<script setup lang="ts">
import { Motion } from "motion-v";
import { ref, onMounted, watchEffect, useSlots } from "vue";

interface Props {
  duration?: number;
  delay?: number;
  blur?: string;
  yOffset?: number;
  class?: string;
}

const props = withDefaults(defineProps<Props>(), {
  duration: 1,
  delay: 2,
  blur: "20px",
  yOffset: 20,
});

const container = ref(null);
const childElements = ref([]);
const slots = useSlots();

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const children = ref<any>([]);

onMounted(() => {
  // This will reactively capture all content provided in the default slot
  watchEffect(() => {
    children.value = slots.default ? slots.default() : [];
  });
});

function getInitial() {
  return {
    opacity: 0,
    filter: `blur(${props.blur})`,
    y: props.yOffset,
  };
}

function getAnimate() {
  return {
    opacity: 1,
    filter: `blur(0px)`,
    y: 0,
  };
}
</script>


<template>
  <div :class="cn('leading-snug tracking-wide', props.class)">
    <div ref="scope">
      <span
        v-for="(word, idx) in wordsArray"
        :key="word + idx"
        class="inline-block"
        :style="spanStyle"
      >
        {{ word }}&nbsp;
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, type HTMLAttributes, onMounted, ref } from "vue";

import { cn } from "@/lib/utils";

const props = withDefaults(
  defineProps<{
    words: string;
    filter?: boolean;
    duration?: number;
    delay?: number;
    class: HTMLAttributes["class"];
  }>(),
  { duration: 0.7, delay: 0, filter: true },
);

const scope = ref(null);
const wordsArray = computed(() => props.words.split(" "));

const spanStyle = computed(() => ({
  opacity: 0,
  filter: props.filter ? "blur(10px)" : "none",
  transition: `opacity ${props.duration}s, filter ${props.duration}s`,
}));

onMounted(() => {
  if (scope.value) {
    const spans = (scope.value as HTMLElement).querySelectorAll("span");

    setTimeout(() => {
      spans.forEach((span: HTMLElement, index: number) => {
        setTimeout(() => {
          span.style.opacity = "1";
          span.style.filter = props.filter ? "blur(0px)" : "none";
        }, index * 200);
      });
    }, props.delay);
  }
});
</script>


